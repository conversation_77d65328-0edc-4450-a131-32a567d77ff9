"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON><PERSON>, <PERSON>, Cloud, <PERSON>f<PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from "lucide-react"

const seasons = [
  { name: "Spring", icon: <Sparkles className="h-5 w-5" />, color: "bg-green-100 text-green-700" },
  { name: "Summer", icon: <Sun className="h-5 w-5" />, color: "bg-yellow-100 text-yellow-700" },
  { name: "Fall", icon: <Cloud className="h-5 w-5" />, color: "bg-orange-100 text-orange-700" },
  { name: "Winter", icon: <Snowflake className="h-5 w-5" />, color: "bg-blue-100 text-blue-700" },
]

const moods = [
  { name: "Energetic", color: "bg-red-100 text-red-700" },
  { name: "Cal<PERSON>", color: "bg-blue-100 text-blue-700" },
  { name: "Romantic", color: "bg-pink-100 text-pink-700" },
  { name: "Professional", color: "bg-gray-100 text-gray-700" },
  { name: "Adventurous", color: "bg-green-100 text-green-700" },
  { name: "Cozy", color: "bg-amber-100 text-amber-700" },
]

const colors = [
  { name: "Red", color: "bg-red-500", textColor: "text-red-700" },
  { name: "Blue", color: "bg-blue-500", textColor: "text-blue-700" },
  { name: "Green", color: "bg-green-500", textColor: "text-green-700" },
  { name: "Yellow", color: "bg-yellow-500", textColor: "text-yellow-700" },
  { name: "Purple", color: "bg-purple-500", textColor: "text-purple-700" },
  { name: "Black", color: "bg-gray-800", textColor: "text-gray-700" },
]

export default function QuickPickEngine() {
  const [selectedSeason, setSelectedSeason] = useState<string | null>(null)
  const [selectedMood, setSelectedMood] = useState<string | null>(null)
  const [selectedColor, setSelectedColor] = useState<string | null>(null)
  const [suggestions, setSuggestions] = useState<any[]>([])

  const generateSuggestions = () => {
    // Mock suggestions based on selections
    const mockSuggestions = [
      {
        name: "Acqua di Gio",
        brand: "Giorgio Armani",
        match: "95%",
        reason: "Perfect for your summer mood",
        layerWith: ["Light Blue", "Cool Water"],
      },
      {
        name: "Bleu de Chanel",
        brand: "Chanel",
        match: "88%",
        reason: "Matches your professional vibe",
        layerWith: ["Sauvage", "Dylan Blue"],
      },
    ]
    setSuggestions(mockSuggestions)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Sparkles className="h-5 w-5 text-purple-600" />
          Quick Pick Engine
        </CardTitle>
        <CardDescription>Find your perfect scent based on season, mood, or outfit</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <h4 className="font-medium mb-3 flex items-center gap-2">
            <Sun className="h-4 w-4" />
            Season
          </h4>
          <div className="grid grid-cols-2 gap-2">
            {seasons.map((season) => (
              <Button
                key={season.name}
                variant={selectedSeason === season.name ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedSeason(season.name)}
                className="justify-start"
              >
                {season.icon}
                <span className="ml-2">{season.name}</span>
              </Button>
            ))}
          </div>
        </div>

        <div>
          <h4 className="font-medium mb-3 flex items-center gap-2">
            <Heart className="h-4 w-4" />
            Mood
          </h4>
          <div className="flex flex-wrap gap-2">
            {moods.map((mood) => (
              <Badge
                key={mood.name}
                variant={selectedMood === mood.name ? "default" : "outline"}
                className="cursor-pointer"
                onClick={() => setSelectedMood(mood.name)}
              >
                {mood.name}
              </Badge>
            ))}
          </div>
        </div>

        <div>
          <h4 className="font-medium mb-3 flex items-center gap-2">
            <Palette className="h-4 w-4" />
            Outfit Color
          </h4>
          <div className="flex flex-wrap gap-2">
            {colors.map((color) => (
              <div
                key={color.name}
                onClick={() => setSelectedColor(color.name)}
                className={`w-8 h-8 rounded-full cursor-pointer border-2 ${color.color} ${
                  selectedColor === color.name ? "border-gray-800" : "border-gray-300"
                }`}
                title={color.name}
              />
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <Button onClick={generateSuggestions} className="w-full">
            Find My Scent
          </Button>
          <Button variant="outline" className="w-full">
            <Dices className="h-4 w-4 mr-2" />
            Surprise me
          </Button>
        </div>

        {suggestions.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium">Suggestions for You</h4>
            {suggestions.map((suggestion, index) => (
              <div key={index} className="border rounded-lg p-3 space-y-2">
                <div className="flex justify-between items-start">
                  <div>
                    <h5 className="font-semibold text-sm">{suggestion.name}</h5>
                    <p className="text-xs text-gray-600">{suggestion.brand}</p>
                  </div>
                  <Badge variant="secondary" className="text-xs">
                    {suggestion.match} match
                  </Badge>
                </div>
                <p className="text-xs text-gray-700">{suggestion.reason}</p>
                <div className="text-xs">
                  <span className="font-medium">Layer with: </span>
                  {suggestion.layerWith.join(", ")}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  )
}
