[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\app\\page.tsx": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\community-feed.tsx": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\my-fragrances.tsx": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\navigation.tsx": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\profile.tsx": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\quick-pick-engine.tsx": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\theme-provider.tsx": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\accordion.tsx": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\alert-dialog.tsx": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\alert.tsx": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\aspect-ratio.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\avatar.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\badge.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\breadcrumb.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\button.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\calendar.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\card.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\carousel.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\chart.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\checkbox.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\collapsible.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\command.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\context-menu.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\dialog.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\drawer.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\dropdown-menu.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\form.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\hover-card.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\input-otp.tsx": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\input.tsx": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\label.tsx": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\menubar.tsx": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\navigation-menu.tsx": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\pagination.tsx": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\popover.tsx": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\progress.tsx": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\radio-group.tsx": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\resizable.tsx": "39", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\scroll-area.tsx": "40", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\select.tsx": "41", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\separator.tsx": "42", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\sheet.tsx": "43", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\sidebar.tsx": "44", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\skeleton.tsx": "45", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\slider.tsx": "46", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\sonner.tsx": "47", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\switch.tsx": "48", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\table.tsx": "49", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\tabs.tsx": "50", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\textarea.tsx": "51", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\toast.tsx": "52", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\toaster.tsx": "53", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\toggle-group.tsx": "54", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\toggle.tsx": "55", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\tooltip.tsx": "56", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\use-mobile.tsx": "57", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\use-toast.ts": "58", "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\lib\\utils.ts": "59"}, {"size": 353, "mtime": 1753040671256, "results": "60", "hashOfConfig": "61"}, {"size": 5312, "mtime": 1753040671265, "results": "62", "hashOfConfig": "61"}, {"size": 12018, "mtime": 1753040671301, "results": "63", "hashOfConfig": "61"}, {"size": 7699, "mtime": 1753040671293, "results": "64", "hashOfConfig": "61"}, {"size": 1247, "mtime": 1753040671314, "results": "65", "hashOfConfig": "61"}, {"size": 9294, "mtime": 1753040671308, "results": "66", "hashOfConfig": "61"}, {"size": 6121, "mtime": 1753040671285, "results": "67", "hashOfConfig": "61"}, {"size": 292, "mtime": 1753040671277, "results": "68", "hashOfConfig": "61"}, {"size": 1991, "mtime": 1753040671326, "results": "69", "hashOfConfig": "61"}, {"size": 4434, "mtime": 1753040671333, "results": "70", "hashOfConfig": "61"}, {"size": 1584, "mtime": 1753040671341, "results": "71", "hashOfConfig": "61"}, {"size": 154, "mtime": 1753040671348, "results": "72", "hashOfConfig": "61"}, {"size": 1419, "mtime": 1753040671355, "results": "73", "hashOfConfig": "61"}, {"size": 1128, "mtime": 1753040671362, "results": "74", "hashOfConfig": "61"}, {"size": 2712, "mtime": 1753040671370, "results": "75", "hashOfConfig": "61"}, {"size": 1901, "mtime": 1753040671377, "results": "76", "hashOfConfig": "61"}, {"size": 2587, "mtime": 1753040671385, "results": "77", "hashOfConfig": "61"}, {"size": 1858, "mtime": 1753040671393, "results": "78", "hashOfConfig": "61"}, {"size": 6224, "mtime": 1753040671400, "results": "79", "hashOfConfig": "61"}, {"size": 10480, "mtime": 1753040671408, "results": "80", "hashOfConfig": "61"}, {"size": 1070, "mtime": 1753040671415, "results": "81", "hashOfConfig": "61"}, {"size": 329, "mtime": 1753040671422, "results": "82", "hashOfConfig": "61"}, {"size": 4899, "mtime": 1753040671429, "results": "83", "hashOfConfig": "61"}, {"size": 7260, "mtime": 1753040671437, "results": "84", "hashOfConfig": "61"}, {"size": 3849, "mtime": 1753040671444, "results": "85", "hashOfConfig": "61"}, {"size": 3021, "mtime": 1753040671451, "results": "86", "hashOfConfig": "61"}, {"size": 7433, "mtime": 1753040671458, "results": "87", "hashOfConfig": "61"}, {"size": 4099, "mtime": 1753040671466, "results": "88", "hashOfConfig": "61"}, {"size": 1198, "mtime": 1753040671473, "results": "89", "hashOfConfig": "61"}, {"size": 2168, "mtime": 1753040671480, "results": "90", "hashOfConfig": "61"}, {"size": 791, "mtime": 1753040671487, "results": "91", "hashOfConfig": "61"}, {"size": 724, "mtime": 1753040671495, "results": "92", "hashOfConfig": "61"}, {"size": 7988, "mtime": 1753040671502, "results": "93", "hashOfConfig": "61"}, {"size": 5046, "mtime": 1753040671511, "results": "94", "hashOfConfig": "61"}, {"size": 2751, "mtime": 1753040671520, "results": "95", "hashOfConfig": "61"}, {"size": 1244, "mtime": 1753040671528, "results": "96", "hashOfConfig": "61"}, {"size": 791, "mtime": 1753040671536, "results": "97", "hashOfConfig": "61"}, {"size": 1481, "mtime": 1753040671544, "results": "98", "hashOfConfig": "61"}, {"size": 1723, "mtime": 1753040671553, "results": "99", "hashOfConfig": "61"}, {"size": 1656, "mtime": 1753040671561, "results": "100", "hashOfConfig": "61"}, {"size": 5629, "mtime": 1753040671568, "results": "101", "hashOfConfig": "61"}, {"size": 770, "mtime": 1753040671576, "results": "102", "hashOfConfig": "61"}, {"size": 4281, "mtime": 1753040671583, "results": "103", "hashOfConfig": "61"}, {"size": 23381, "mtime": 1753040671592, "results": "104", "hashOfConfig": "61"}, {"size": 261, "mtime": 1753040671599, "results": "105", "hashOfConfig": "61"}, {"size": 1091, "mtime": 1753040671607, "results": "106", "hashOfConfig": "61"}, {"size": 894, "mtime": 1753040671614, "results": "107", "hashOfConfig": "61"}, {"size": 1153, "mtime": 1753040671622, "results": "108", "hashOfConfig": "61"}, {"size": 2765, "mtime": 1753040671631, "results": "109", "hashOfConfig": "61"}, {"size": 1897, "mtime": 1753040671638, "results": "110", "hashOfConfig": "61"}, {"size": 689, "mtime": 1753040671646, "results": "111", "hashOfConfig": "61"}, {"size": 4859, "mtime": 1753040671654, "results": "112", "hashOfConfig": "61"}, {"size": 786, "mtime": 1753040671665, "results": "113", "hashOfConfig": "61"}, {"size": 1753, "mtime": 1753040671673, "results": "114", "hashOfConfig": "61"}, {"size": 1541, "mtime": 1753040671680, "results": "115", "hashOfConfig": "61"}, {"size": 1159, "mtime": 1753040671689, "results": "116", "hashOfConfig": "61"}, {"size": 565, "mtime": 1753040671696, "results": "117", "hashOfConfig": "61"}, {"size": 3948, "mtime": 1753040671705, "results": "118", "hashOfConfig": "61"}, {"size": 166, "mtime": 1753040671741, "results": "119", "hashOfConfig": "61"}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "kzu7da", {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\community-feed.tsx", ["297"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\my-fragrances.tsx", ["298"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\navigation.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\profile.tsx", ["299", "300"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\quick-pick-engine.tsx", ["301"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\theme-provider.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\accordion.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\alert-dialog.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\alert.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\aspect-ratio.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\avatar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\breadcrumb.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\calendar.tsx", ["302", "303"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\carousel.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\chart.tsx", ["304"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\checkbox.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\collapsible.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\command.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\context-menu.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\drawer.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\dropdown-menu.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\hover-card.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\input-otp.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\menubar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\navigation-menu.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\pagination.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\popover.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\radio-group.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\resizable.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\scroll-area.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\select.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\separator.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\sheet.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\sidebar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\skeleton.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\slider.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\sonner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\switch.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\tabs.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\toast.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\toaster.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\toggle-group.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\toggle.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\tooltip.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\use-mobile.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\components\\ui\\use-toast.ts", ["305"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\Perfume App\\lib\\utils.ts", [], [], {"ruleId": "306", "severity": 2, "message": "307", "line": 140, "column": 39, "nodeType": "308", "messageId": "309", "suggestions": "310"}, {"ruleId": "311", "severity": 1, "message": "312", "line": 55, "column": 22, "nodeType": null, "messageId": "313", "endLine": 55, "endColumn": 35}, {"ruleId": "306", "severity": 2, "message": "314", "line": 181, "column": 37, "nodeType": "308", "messageId": "309", "suggestions": "315"}, {"ruleId": "306", "severity": 2, "message": "314", "line": 181, "column": 56, "nodeType": "308", "messageId": "309", "suggestions": "316"}, {"ruleId": "317", "severity": 2, "message": "318", "line": 38, "column": 50, "nodeType": "319", "messageId": "320", "endLine": 38, "endColumn": 53, "suggestions": "321"}, {"ruleId": "311", "severity": 1, "message": "322", "line": 57, "column": 25, "nodeType": null, "messageId": "313", "endLine": 57, "endColumn": 30}, {"ruleId": "311", "severity": 1, "message": "322", "line": 58, "column": 26, "nodeType": null, "messageId": "313", "endLine": 58, "endColumn": 31}, {"ruleId": "311", "severity": 1, "message": "323", "line": 72, "column": 7, "nodeType": null, "messageId": "313", "endLine": 72, "endColumn": 8}, {"ruleId": "311", "severity": 1, "message": "324", "line": 21, "column": 7, "nodeType": null, "messageId": "325", "endLine": 21, "endColumn": 18}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["326", "327", "328", "329"], "@typescript-eslint/no-unused-vars", "'setFragrances' is assigned a value but never used.", "unusedVar", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["330", "331", "332", "333"], ["334", "335", "336", "337"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["338", "339"], "'props' is defined but never used.", "'_' is defined but never used.", "'actionTypes' is assigned a value but only used as a type.", "usedOnlyAsType", {"messageId": "340", "data": "341", "fix": "342", "desc": "343"}, {"messageId": "340", "data": "344", "fix": "345", "desc": "346"}, {"messageId": "340", "data": "347", "fix": "348", "desc": "349"}, {"messageId": "340", "data": "350", "fix": "351", "desc": "352"}, {"messageId": "340", "data": "353", "fix": "354", "desc": "355"}, {"messageId": "340", "data": "356", "fix": "357", "desc": "358"}, {"messageId": "340", "data": "359", "fix": "360", "desc": "361"}, {"messageId": "340", "data": "362", "fix": "363", "desc": "364"}, {"messageId": "340", "data": "365", "fix": "366", "desc": "355"}, {"messageId": "340", "data": "367", "fix": "368", "desc": "358"}, {"messageId": "340", "data": "369", "fix": "370", "desc": "361"}, {"messageId": "340", "data": "371", "fix": "372", "desc": "364"}, {"messageId": "373", "fix": "374", "desc": "375"}, {"messageId": "376", "fix": "377", "desc": "378"}, "replaceWithAlt", {"alt": "379"}, {"range": "380", "text": "381"}, "Replace with `&apos;`.", {"alt": "382"}, {"range": "383", "text": "384"}, "Replace with `&lsquo;`.", {"alt": "385"}, {"range": "386", "text": "387"}, "Replace with `&#39;`.", {"alt": "388"}, {"range": "389", "text": "390"}, "Replace with `&rsquo;`.", {"alt": "391"}, {"range": "392", "text": "393"}, "Replace with `&quot;`.", {"alt": "394"}, {"range": "395", "text": "396"}, "Replace with `&ldquo;`.", {"alt": "397"}, {"range": "398", "text": "399"}, "Replace with `&#34;`.", {"alt": "400"}, {"range": "401", "text": "402"}, "Replace with `&rdquo;`.", {"alt": "391"}, {"range": "403", "text": "404"}, {"alt": "394"}, {"range": "405", "text": "406"}, {"alt": "397"}, {"range": "407", "text": "408"}, {"alt": "400"}, {"range": "409", "text": "410"}, "suggestUnknown", {"range": "411", "text": "412"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "413", "text": "414"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "&apos;", [4752, 4795], "Community&apos;s favorite fragrance combinations", "&lsquo;", [4752, 4795], "Community&lsquo;s favorite fragrance combinations", "&#39;", [4752, 4795], "Community&#39;s favorite fragrance combinations", "&rsquo;", [4752, 4795], "Community&rsquo;s favorite fragrance combinations", "&quot;", [6613, 6647], "Created stack &quot;Morning Confidence\"", "&ldquo;", [6613, 6647], "Created stack &ldquo;Morning Confidence\"", "&#34;", [6613, 6647], "Created stack &#34;Morning Confidence\"", "&rdquo;", [6613, 6647], "Created stack &rdquo;Morning Confidence\"", [6613, 6647], "Created stack \"Morning Confidence&quot;", [6613, 6647], "Created stack \"Morning Confidence&ldquo;", [6613, 6647], "Created stack \"Morning Confidence&#34;", [6613, 6647], "Created stack \"Morning Confidence&rdquo;", [1890, 1893], "unknown", [1890, 1893], "never"]